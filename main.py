# -*- coding: utf-8 -*-
import argparse
import os
import sys

import argh
import talib

import quant.strategy
from quant.com.config import init_config, app_config
from quant.com.log import init_logging, logger


@argh.arg('--config', help = f"配置文件路径")
@argh.arg('--version', help = '显示版本信息')
@argh.arg('--debug', help = '设置日志级别为debug, 默认为info级别')
def start_live(config: str = '', version: bool = False, debug: bool = False):
    """
    实盘模式启动策略,此命令为默认命令(即未指定子命令时,即执行此默认命令)
    """
    if version:
        print(f'python version : {sys.version}')
        print(f"version : {app_config().get_string('version')}")
        print(f"talib version : {talib.__version__}")
        print(f"git hash : {app_config().get_string('git_hash')}")
        print(f"build at : {app_config().get_string('build_at')}")
        return

    if config == '':
        raise '未设置 --config 参数'
    init_config(fpath = config)
    init_logging(force_debug = debug)
    cfg = app_config()

    logger.debug(f'python version : {sys.version}')
    logger.debug(f"talib version : {talib.__version__}")
    logger.debug(f"当前工作目录: {os.getcwd()}")
    logger.info(f"version : {cfg.get_string('version')}")
    logger.info(f"git hash : {cfg.get_string('git_hash')}")
    logger.info(f"build at : {cfg.get_string('build_at')}")
    logger.debug(f"token : {cfg.get_string('token')}")
    # 这里hack处理一下, 因为 gm.api.basic 里面的 run 函数, 有对命令行参数进行解析的处理, 是不会识别 --config 参数的, 所以这里把 --config 去掉
    argv_0 = sys.argv[0]
    sys.argv = [argv_0]
    quant.strategy.start()


@argh.arg('--config', help = f"配置文件路径")
@argh.arg('--debug', help = '设置日志级别为debug, 默认为info级别')
def start_backtest(config: str = '', debug: bool = False):
    """
    回测模式启动策略,用于开发调试
    """
    init_config(fpath = config)
    init_logging(force_debug = debug)
    argv_0 = sys.argv[0]
    sys.argv = [argv_0]
    logger.debug(f'DEBUG 模式: {debug}')
    quant.strategy.start_backtest()


def main():
    parser = argparse.ArgumentParser()
    argh.add_commands(parser, [start_live, start_backtest])
    argh.set_default_command(parser, start_live)
    argh.dispatch(parser)


if __name__ == "__main__":
    main()
