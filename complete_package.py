# -*- coding: utf-8 -*-
"""
完整的策略打包流程脚本
执行命令: python complete_package.py
"""
import os
import shutil
import subprocess
from pathlib import Path

def run_command(cmd, description=""):
    """执行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8', errors='ignore')
        print("✅ 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def clean_environment():
    """清理环境"""
    print("🧹 开始清理环境...")
    
    # 清理构建目录
    for dir_name in ['build', 'dist', '__pycache__']:
        dir_path = Path(dir_name)
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 删除目录: {dir_path}")
            except Exception as e:
                print(f"⚠️ 删除目录失败 {dir_path}: {e}")
    
    # 删除spec文件
    for spec_file in Path('.').glob('*.spec'):
        try:
            spec_file.unlink()
            print(f"✅ 删除spec文件: {spec_file}")
        except Exception as e:
            print(f"⚠️ 删除spec文件失败 {spec_file}: {e}")

def rebuild_venv():
    """重建虚拟环境"""
    print("🔄 重建虚拟环境...")
    
    # 删除虚拟环境
    venv_path = Path(".venv")
    if venv_path.exists():
        print("🗑️ 删除现有虚拟环境...")
        try:
            shutil.rmtree(venv_path)
            print("✅ 虚拟环境删除成功")
        except Exception as e:
            print(f"❌ 删除虚拟环境失败: {e}")
            print("请手动删除.venv目录后重新运行")
            return False
    
    # 重新同步环境
    if not run_command("uv sync", "重新同步环境"):
        return False
    
    # 安装PyInstaller
    if not run_command("uv pip install pyinstaller", "安装PyInstaller"):
        return False
    
    return True

def remove_typing_package():
    """删除typing包"""
    print("🗑️ 删除typing包...")
    
    typing_files = [
        Path(".venv/lib/site-packages/typing.py"),
        Path(".venv/lib/site-packages/typing"),
    ]
    
    # 删除typing目录信息（使用通配符）
    site_packages = Path(".venv/lib/site-packages")
    if site_packages.exists():
        typing_dirs = list(site_packages.glob("typing-*"))
        typing_files.extend(typing_dirs)
    
    removed_count = 0
    for file_path in typing_files:
        if file_path.exists():
            try:
                if file_path.is_file():
                    file_path.unlink()
                    print(f"✅ 删除文件: {file_path}")
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
                    print(f"✅ 删除目录: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")
    
    print(f"ℹ️ 删除了 {removed_count} 个typing相关文件")
    return True

def package_strategy():
    """打包策略"""
    print("📦 开始打包策略...")
    
    # 使用虚拟环境的PyInstaller
    pyinstaller_path = ".venv\\Scripts\\pyinstaller.exe"
    if not Path(pyinstaller_path).exists():
        print(f"❌ PyInstaller不存在: {pyinstaller_path}")
        return False
    
    # 在打包前删除typing包
    remove_typing_package()
    
    # 立即执行PyInstaller打包（不要运行其他uv命令）
    cmd = f'"{pyinstaller_path}" -D -n launcher main.py --clean'
    if not run_command(cmd, "PyInstaller打包"):
        return False
    
    # 执行后处理
    if not run_command("uv run python build.py", "后处理和压缩"):
        return False
    
    return True

def verify_result():
    """验证打包结果"""
    print("🔍 验证打包结果...")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 查找zip文件
    zip_files = list(dist_dir.glob("strategy_*.zip"))
    if not zip_files:
        print("❌ 未找到策略zip文件")
        return False
    
    print("🎉 打包成功!")
    for zip_file in zip_files:
        size_mb = zip_file.stat().st_size / 1024 / 1024
        print(f"📦 生成文件: {zip_file}")
        print(f"📁 文件大小: {size_mb:.2f} MB")
    
    return True

def main():
    """主函数"""
    print("🚀 开始完整的全市场日内选股策略打包流程")
    print("=" * 80)
    
    # 检查当前目录
    if not Path('main.py').exists():
        print("❌ 错误: 请在项目根目录执行此脚本")
        return False
    
    # 步骤1: 清理环境
    clean_environment()
    
    # 步骤2: 重建虚拟环境
    if not rebuild_venv():
        print("\n❌ 虚拟环境重建失败!")
        return False
    
    # 步骤3: 打包策略
    if not package_strategy():
        print("\n❌ 策略打包失败!")
        return False
    
    # 步骤4: 验证结果
    if not verify_result():
        print("\n❌ 打包结果验证失败!")
        return False
    
    print("\n" + "=" * 80)
    print("✅ 全市场日内选股策略打包流程成功完成!")
    print("📋 生成的文件可以直接部署到策略管理系统")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 打包流程失败!")
        exit(1)
