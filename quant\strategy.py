# -*- coding: utf-8 -*-
from __future__ import print_function, absolute_import, unicode_literals

from datetime import datetime, timedelta
import pandas as pd

from gm.api import *

from quant.com.config import app_config
from quant.com.log import logger

cfg = app_config()

"""
全市场日内选股策略
基于技术指标和财务数据的多因子选股策略，定时选股，支持止盈止损和仓位管理
"""

# ==================== 技术指标计算函数 ====================
def calculate_ma(prices, period):
    """计算移动平均线"""
    if len(prices) < period:
        return None
    return sum(prices[-period:]) / period

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    if len(prices) < period + 1:
        return None

    gains = []
    losses = []

    for i in range(1, len(prices)):
        change = prices[i] - prices[i-1]
        if change > 0:
            gains.append(change)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(-change)

    if len(gains) < period:
        return None

    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period

    if avg_loss == 0:
        return 100

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_volume_ratio(current_volume, avg_volume):
    """计算量比"""
    if avg_volume == 0:
        return 0
    return current_volume / avg_volume


def init(context):
    """
    初始化策略,策略启动即会自动执行。在 init 函数中可以：
    * 定义全局变量 通过添加context包含的属性可以定义全局变量，如 context.x,该属性可以在全文中进行传递。
    * 定义调度任务 可以通过schedule配置定时任务，程序在指定时间自动执行策略算法。
    * 准备历史数据 通过数据查询函数获取历史数据
    * 订阅实时行情 通过subscribe订阅行情，用以触发行情事件处理函数。
    """
    logger.info("全市场日内选股策略初始化")

    # 从配置中读取参数
    context.fixed_take_profit = cfg.get_float('fixed_take_profit', 0.05)  # 固定止盈
    context.floating_take_profit = cfg.get_float('floating_take_profit', 0.02)  # 浮动止盈
    context.fixed_stop_loss = cfg.get_float('fixed_stop_loss', 0.03)  # 固定止损
    context.position_size_ratio = cfg.get_float('position_size_ratio', 0.2)  # 单股持仓比例
    context.max_positions = cfg.get_int('max_positions', 10)  # 最大持仓数量

    logger.info(f"策略参数 - 固定止盈: {context.fixed_take_profit:.3f}, 浮动止盈: {context.floating_take_profit:.3f}")
    logger.info(f"策略参数 - 固定止损: {context.fixed_stop_loss:.3f}, 单股持仓: {context.position_size_ratio:.2f}, 最大持仓: {context.max_positions}")

    # 选股条件参数（固定值）
    context.price_change_min = 0.02      # 涨幅下限 2%
    context.price_change_max = 0.05      # 涨幅上限 5%
    context.volume_ratio_min = 2.0       # 量比最小值 2.0倍
    context.turnover_min = 0.05          # 换手率下限 5%
    context.turnover_max = 0.10          # 换手率上限 10%
    context.market_cap_min = 300000      # 流通市值下限 30亿(万元)
    context.market_cap_max = 2000000     # 流通市值上限 200亿(万元)
    context.rsi_min = 50                 # RSI最小值

    # 均线参数
    context.ma_short = 5                 # 短期均线
    context.ma_medium = 10               # 中期均线
    context.ma_long = 20                 # 长期均线

    # 策略状态变量
    context.selected_stocks = set()      # 已选中的股票
    context.selected_stocks_details = [] # 选中股票的详细信息
    context.stock_buy_prices = {}        # 股票买入价格
    context.stock_daily_highs = {}       # 股票当日最高价
    context.daily_stats = {}             # 每日选股统计
    context.executed_orders = set()      # 已执行订单集合
    context.strategy_state = 'SELECTING' # 策略状态

    # 获取全市场股票池
    try:
        logger.info("正在获取全市场股票池...")
        instruments = get_instruments(exchanges=['SHSE', 'SZSE'], sec_types=[SEC_TYPE_STOCK], df=True)
        if instruments is not None and not instruments.empty:
            # 过滤掉ST、退市等股票
            valid_instruments = instruments[
                (~instruments['symbol'].str.contains('ST')) &
                (~instruments['symbol'].str.contains('退')) &
                (instruments['listed_date'] <= context.now.strftime('%Y-%m-%d'))
            ]
            context.stock_symbols = valid_instruments['symbol'].tolist()
            logger.info(f"获取股票池成功: {len(context.stock_symbols)}只股票")
        else:
            # 使用备选股票池
            context.stock_symbols = ['SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SZSE.000002', 'SZSE.000001']
            logger.warning(f"使用备选股票池: {len(context.stock_symbols)}只股票")
    except Exception as e:
        logger.error(f"获取股票池失败: {e}")
        context.stock_symbols = ['SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SZSE.000002', 'SZSE.000001']
        logger.warning(f"使用最小股票池: {len(context.stock_symbols)}只股票")

    # 定义调度任务
    schedule(schedule_func=morning_stock_selection, date_rule='1d', time_rule='11:00:00')
    schedule(schedule_func=afternoon_stock_selection, date_rule='1d', time_rule='14:00:00')

    # 查询跟策略绑定的所有交易账号
    for account_id in context.accounts:
        logger.info(f"策略已关联交易账号: {account_id}")

    logger.info("策略初始化完成")


def morning_stock_selection(context):
    """上午选股任务"""
    if len(context.selected_stocks) < context.max_positions:
        execute_stock_selection(context, 'morning')
    else:
        logger.info("持仓已满，跳过上午选股")

def afternoon_stock_selection(context):
    """下午选股任务"""
    if len(context.selected_stocks) < context.max_positions:
        execute_stock_selection(context, 'afternoon')
    else:
        logger.info("持仓已满，跳过下午选股")

def execute_stock_selection(context, session):
    """执行选股逻辑"""
    logger.info(f"开始{session}选股...")

    # 获取候选股票（涨幅在2%-5%之间）
    candidate_symbols = []
    current_date = context.now.strftime('%Y-%m-%d')

    for symbol in context.stock_symbols[:500]:  # 限制检查数量避免超时
        try:
            # 获取当日行情数据
            hist_data = history_n(symbol=symbol, frequency='1d', count=1, end_time=context.now, df=True)
            if hist_data is None or hist_data.empty:
                continue

            current_price = hist_data.iloc[-1]['close']
            open_price = hist_data.iloc[-1]['open']

            # 计算涨幅
            if open_price > 0:
                price_change = (current_price - open_price) / open_price
                if context.price_change_min <= price_change <= context.price_change_max:
                    candidate_symbols.append(symbol)

        except Exception as e:
            continue

    logger.info(f"候选股票池: {len(candidate_symbols)}只")

    if candidate_symbols:
        # 进行详细筛选
        selected_symbols = unified_stock_selection(context, candidate_symbols)

        # 买入选中的股票
        for symbol in selected_symbols:
            if len(context.selected_stocks) < context.max_positions and symbol not in context.selected_stocks:
                buy_stock(context, symbol)

def unified_stock_selection(context, candidate_symbols):
    """统一选股筛选"""
    selected_symbols = []
    current_date = context.now.strftime('%Y-%m-%d')

    for symbol in candidate_symbols:
        try:
            # 获取历史数据用于技术指标计算
            hist_data = history_n(symbol=symbol, frequency='1d', count=context.ma_long + 5, end_time=context.now, df=True)
            if hist_data is None or len(hist_data) < context.ma_long:
                continue

            prices = hist_data['close'].tolist()
            volumes = hist_data['volume'].tolist()

            # 计算技术指标
            ma5 = calculate_ma(prices, context.ma_short)
            ma10 = calculate_ma(prices, context.ma_medium)
            ma20 = calculate_ma(prices, context.ma_long)
            rsi = calculate_rsi(prices)

            if not all([ma5, ma10, ma20, rsi]):
                continue

            # 均线多头排列检查
            if not (ma5 >= ma10 > ma20):
                continue

            # RSI检查
            if rsi <= context.rsi_min:
                continue

            # 量比检查
            current_volume = volumes[-1]
            avg_volume = sum(volumes[-5:]) / 5 if len(volumes) >= 5 else current_volume
            volume_ratio = calculate_volume_ratio(current_volume, avg_volume)

            if volume_ratio < context.volume_ratio_min:
                continue

            # 获取市值和换手率数据
            market_cap = get_market_cap(symbol, current_date)
            turnover_rate = get_turnover_rate(symbol, current_date)

            if not market_cap or not turnover_rate:
                continue

            # 市值检查
            if not (context.market_cap_min <= market_cap <= context.market_cap_max):
                continue

            # 换手率检查
            if not (context.turnover_min <= turnover_rate <= context.turnover_max):
                continue

            # 通过所有筛选条件
            selected_symbols.append(symbol)
            logger.info(f"选中股票: {symbol}, MA5:{ma5:.2f}, RSI:{rsi:.1f}, 量比:{volume_ratio:.2f}")

            # 限制选股数量
            if len(selected_symbols) >= 5:
                break

        except Exception as e:
            logger.error(f"筛选股票{symbol}时出错: {e}")
            continue

    return selected_symbols

def get_market_cap(symbol, trade_date):
    """获取流通市值"""
    try:
        mktvalue_data = stk_get_daily_mktvalue_pt(
            symbols=[symbol],
            fields='a_mv',
            trade_date=trade_date,
            df=True
        )
        if not mktvalue_data.empty:
            return mktvalue_data.iloc[0]['a_mv'] / 10000  # 转换为万元
    except:
        pass
    return None

def get_turnover_rate(symbol, trade_date):
    """获取换手率"""
    try:
        # 使用history获取换手率数据
        hist_data = history_n(symbol=symbol, frequency='1d', count=1, end_time=trade_date + ' 15:00:00', df=True)
        if hist_data is not None and not hist_data.empty and 'turn' in hist_data.columns:
            return hist_data.iloc[-1]['turn'] / 100  # 转换为小数
    except:
        pass
    return None

def buy_stock(context, symbol):
    """买入股票"""
    try:
        # 获取当前价格
        current_data = history_n(symbol=symbol, frequency='1d', count=1, end_time=context.now, df=False)
        if not current_data:
            return

        current_price = current_data[0]['close']

        # 计算买入金额
        account = context.account()
        available_cash = account.cash['available']
        buy_amount = available_cash * context.position_size_ratio

        if buy_amount < 1000:  # 最小买入金额
            logger.warning(f"可用资金不足，跳过买入{symbol}")
            return

        # 执行买入
        order = order_target_value(symbol=symbol, value=buy_amount, side=OrderSide_Buy,
                                   order_type=OrderType_Market, position_effect=PositionEffect_Open)

        if order:
            context.selected_stocks.add(symbol)
            context.stock_buy_prices[symbol] = current_price
            context.stock_daily_highs[symbol] = current_price
            logger.info(f"买入股票: {symbol}, 价格: {current_price:.2f}, 金额: {buy_amount:.0f}")

    except Exception as e:
        logger.error(f"买入股票{symbol}失败: {e}")

def on_bar(context, bars):
    """
    bar 数据推送事件, 接收固定周期 bar 数据， 通过 subscribe 订阅 bar 行情，行情服务主动推送 bar 数据
    处理分时bar数据事件 通过on_bar响应 bar 数据事件，可以在该函数中继续添加自己的策略逻辑，如进行数据计算、交易等
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E6%95%B0%E6%8D%AE%E4%BA%8B%E4%BB%B6.html#on-tick-tick-%E6%95%B0%E6%8D%AE%E6%8E%A8%E9%80%81%E4%BA%8B%E4%BB%B6
    """
    for bar in bars:
        if bar.frequency == '1d':
            handle_position_management(context, bar)


def handle_position_management(context, bar):
    """处理持仓管理和止盈止损"""
    symbol = bar.symbol
    current_price = bar.close

    if symbol not in context.selected_stocks:
        return

    # 更新当日最高价
    if symbol in context.stock_daily_highs:
        context.stock_daily_highs[symbol] = max(context.stock_daily_highs[symbol], current_price)
    else:
        context.stock_daily_highs[symbol] = current_price

    # 获取买入价格
    buy_price = context.stock_buy_prices.get(symbol)
    if not buy_price:
        return

    # 计算盈亏比例
    profit_ratio = (current_price - buy_price) / buy_price
    daily_high = context.stock_daily_highs[symbol]

    # 获取持仓信息
    positions = get_position()
    position = None
    for pos in positions:
        if pos['symbol'] == symbol:
            position = pos
            break

    if not position or position['volume'] <= 0:
        return

    # 止盈止损逻辑
    sell_reason = None

    # 固定止损
    if profit_ratio <= -context.fixed_stop_loss:
        sell_reason = "固定止损"
    # 固定止盈
    elif profit_ratio >= context.fixed_take_profit:
        sell_reason = "固定止盈"
    # 浮动止盈（突破当日高点后回落）
    elif current_price < daily_high * (1 - context.floating_take_profit) and daily_high > buy_price:
        sell_reason = "浮动止盈"
    # 破5日均线止损
    else:
        try:
            hist_data = history_n(symbol=symbol, frequency='1d', count=context.ma_short + 1, end_time=context.now, df=True)
            if hist_data is not None and len(hist_data) >= context.ma_short:
                prices = hist_data['close'].tolist()
                ma5 = calculate_ma(prices, context.ma_short)
                if ma5 and current_price < ma5:
                    sell_reason = "破5日均线"
        except:
            pass

    if sell_reason:
        sell_stock(context, symbol, current_price, position, sell_reason)

def sell_stock(context, symbol, current_price, position, reason):
    """卖出股票"""
    try:
        # 执行卖出
        order = order_target_percent(symbol=symbol, percent=0, side=OrderSide_Sell,
                                     order_type=OrderType_Market, position_side=PositionSide_Long)

        if order:
            # 更新状态
            context.selected_stocks.discard(symbol)
            buy_price = context.stock_buy_prices.get(symbol, current_price)
            profit_ratio = (current_price - buy_price) / buy_price if buy_price > 0 else 0

            logger.info(f"卖出股票: {symbol}, 价格: {current_price:.2f}, 原因: {reason}, 盈亏: {profit_ratio:.2%}")

            # 清理记录
            context.stock_buy_prices.pop(symbol, None)
            context.stock_daily_highs.pop(symbol, None)

    except Exception as e:
        logger.error(f"卖出股票{symbol}失败: {e}")

def on_tick(context, tick):
    """
    tick 数据推送事件, 接收 tick 分笔数据， 通过 subscribe 订阅 tick 行情，行情服务主动推送 tick 数据
    处理盘口tick数据事件 通过on_tick响应 tick 数据事件，可以在该函数中继续添加自己的策略逻辑,如进行数据计算、交易等
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E6%95%B0%E6%8D%AE%E4%BA%8B%E4%BB%B6.html#on-bar-bar-%E6%95%B0%E6%8D%AE%E6%8E%A8%E9%80%81%E4%BA%8B%E4%BB%B6
    """
    pass


def on_execution_report(context, execrpt):
    """
    委托执行回报事件, 响应委托被执行事件，委托成交或者撤单拒绝后被触发。
    注意：
        1. 交易账户重连后，会重新推送一遍交易账户登录成功后查询回来的所有执行回报，防止在交易账户断线期间有执行回报没有推送。
        2. 撤单拒绝后，会推送撤单拒绝执行回报，可以根据 exec_id 区分
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E4%BA%A4%E6%98%93%E4%BA%8B%E4%BB%B6.html#on-execution-report-%E5%A7%94%E6%89%98%E6%89%A7%E8%A1%8C%E5%9B%9E%E6%8A%A5%E4%BA%8B%E4%BB%B6
    """
    logger.info(f"委托执行回报 - 股票: {execrpt.symbol}, 成交量: {execrpt.volume}, 成交价: {execrpt.price:.2f}")


def on_order_status(context, order):
    """
    委托状态更新事件
    响应委托状态更新事件，下单后及委托状态更新时被触发。
    注意：
        1. 交易账户重连后，会重新推送一遍交易账户登录成功后查询回来的所有委托的最终状态，防止断线期间有委托状态变化没有推送。
        2. 撤单拒绝，会推送撤单委托的最终状态。
        3. 回测模式下，交易事件顺序与实时模式一致，委托状态 待报 和 已报 是虚拟状态，不会更新持仓和资金。已成状态后才会更新持仓和资金。
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E4%BA%A4%E6%98%93%E4%BA%8B%E4%BB%B6.html#on-order-status-%E5%A7%94%E6%89%98%E7%8A%B6%E6%80%81%E6%9B%B4%E6%96%B0%E4%BA%8B%E4%BB%B6
    """
    logger.info(f"委托状态更新 - 股票: {order.symbol}, 状态: {order.status}, 方向: {order.side}")


def on_account_status(context, account):
    """
    交易账户状态更新事件, 响应交易账户状态更新事件，交易账户状态变化时被触发。
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E4%BA%A4%E6%98%93%E4%BA%8B%E4%BB%B6.html#on-account-status-%E4%BA%A4%E6%98%93%E8%B4%A6%E6%88%B7%E7%8A%B6%E6%80%81%E6%9B%B4%E6%96%B0%E4%BA%8B%E4%BB%B6
    """
    pass


def on_error(context, code, info):
    """
    当发生异常情况，比如断网时、终端服务崩溃是会触发
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E5%85%B6%E4%BB%96%E4%BA%8B%E4%BB%B6.html#on-error-%E9%94%99%E8%AF%AF%E4%BA%8B%E4%BB%B6
    """
    pass


def on_market_data_connected(context):
    """
    实时行情网络连接时触发，比如策略实时运行启动后会触发、行情断连又重连后会触发
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E5%85%B6%E4%BB%96%E4%BA%8B%E4%BB%B6.html#on-market-data-connected-%E5%AE%9E%E6%97%B6%E8%A1%8C%E6%83%85%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%E6%88%90%E5%8A%9F%E4%BA%8B%E4%BB%B6
    """
    pass


def on_market_data_disconnected(context):
    """
    实时行情网络断开时触发，比如策略实时运行行情断连会触发
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E5%85%B6%E4%BB%96%E4%BA%8B%E4%BB%B6.html#on-market-data-disconnected-%E5%AE%9E%E6%97%B6%E8%A1%8C%E6%83%85%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%E6%96%AD%E5%BC%80%E4%BA%8B%E4%BB%B6
    """
    pass


def on_trade_data_connected(context):
    """
    目前监控 SDK 的交易和终端的链接情况，终端之后部分暂未做在内。账号连接情况可通过终端内账户连接指示灯查看
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E5%85%B6%E4%BB%96%E4%BA%8B%E4%BB%B6.html#on-trade-data-connected-%E4%BA%A4%E6%98%93%E9%80%9A%E9%81%93%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%E6%88%90%E5%8A%9F%E4%BA%8B%E4%BB%B6
    """
    pass


def on_trade_data_disconnected(context):
    """
    目前监控 SDK 的交易和终端的链接情况，终端交易服务崩溃后会触发，终端之后部分暂未做在内。账号连接情况可通过终端内账户连接指示灯查看
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E5%85%B6%E4%BB%96%E4%BA%8B%E4%BB%B6.html#on-trade-data-disconnected-%E4%BA%A4%E6%98%93%E9%80%9A%E9%81%93%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%E6%96%AD%E5%BC%80%E4%BA%8B%E4%BB%B6
    """
    pass


def on_backtest_finished(context, indicator):
    """
    在回测模式下，回测结束后会触发该事件，并返回回测得到的绩效指标对象
    文档: https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E5%85%B6%E4%BB%96%E4%BA%8B%E4%BB%B6.html#on-backtest-finished-%E5%9B%9E%E6%B5%8B%E7%BB%93%E6%9D%9F%E4%BA%8B%E4%BB%B6
    """
    print('*' * 50)
    print('回测已完成，请通过右上角“回测历史”功能查询详情。')


def start():
    """
    启动策略, 由打包后的 lancher.exe 调用
    """
    logger.info(f"启动策略, 策略id: {cfg.get_string('strategyId')}")
    run(strategy_id = cfg.get_string("strategyId"),
        filename = 'quant/strategy.py',
        mode = MODE_LIVE,
        token = cfg.get_string("token"))


def start_backtest():
    logger.info(f"启动策略, 回测模式, 策略id: {cfg.get_string('strategyId')}")

    backtest_start_time = str(datetime.now() - timedelta(days = 180))[:19]
    backtest_end_time = str(datetime.now())[:19]
    run(strategy_id = cfg.get_string("strategyId"),
        filename = 'quant/strategy.py',
        mode = MODE_BACKTEST,
        token = cfg.get_string("token"),
        backtest_start_time = backtest_start_time,
        backtest_end_time = backtest_end_time,
        backtest_adjust = ADJUST_PREV,
        backtest_initial_cash = 200000,
        backtest_commission_ratio = 0.0001,
        backtest_slippage_ratio = 0.0001,
        backtest_match_mode = 1)
