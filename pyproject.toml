[project]
name = "py-strategy-template"
version = "0.1.0"
description = "掘金Python策略模板"
readme = "README.md"
requires-python = ">=3.8,<3.9"
dependencies = [
    "argh>=0.31.3",
    "gm>=3.0.172",
    "pyhocon>=0.3.61",
    "ta-lib",
    "pandas>=1.3.0",
]

[tool.uv]
link-mode = "copy"

# 添加ta-lib的whl文件
[tool.uv.sources]
ta-lib = { path = "ta_lib-0.5.1-cp38-cp38-win_amd64.whl" }

# 指定pip使用清华源
[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true

