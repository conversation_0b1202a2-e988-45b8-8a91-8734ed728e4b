# -*- coding: utf-8 -*-

import logging
import os.path
from datetime import time
from logging.handlers import TimedRotatingFileHandler
from pyhocon import ConfigTree

from quant.com.config import app_config

logger = logging.getLogger(name = "app")

default_fmt = "%(asctime)s [%(name)s] [%(levelname)s] [%(threadName)s] - %(message)s"

log_level_name_map = {
    "CRITICAL": 50,
    "ERROR": 40,
    "WARNING": 30,
    "INFO": 20,
    "DEBUG": 10,
    "NOTSET": 0
}


def init_logging(force_debug = False):
    cfg = app_config()
    log_level = get_default_logger_level()
    if force_debug:
        # debug 模式, 强制日志级别为 debug
        log_level = log_level_name_map["DEBUG"]

    log_fmt = cfg.get_string("log.format", default = default_fmt)
    logging.basicConfig(level = log_level, format = log_fmt)

    default_log_file_name = cfg.get_string("log.default.file_name", default = '')
    if default_log_file_name != '':
        # 有设置log的输出文件, 则进行相应的的初始化
        # 获取log文件的路径所在的目录
        log_dir = os.path.dirname(os.path.abspath(default_log_file_name))
        # 创建log文件目录,如果不存在的话
        os.makedirs(log_dir, exist_ok = True)
        # 设置log文件保留策略
        default_log_file_backup_count = cfg.get_int("log.default.backup_count", default = 3)
        default_file_handler = TimedRotatingFileHandler(filename = default_log_file_name,
                                                        encoding = 'utf-8',
                                                        when = 'D',
                                                        backupCount = default_log_file_backup_count,
                                                        atTime = time.fromisoformat("00:00:00"))
        # 设置log文件的日志输出格式
        fmt = logging.Formatter(log_fmt)
        default_file_handler.setFormatter(fmt)
        # 添加注册log文件的处理器
        logging.root.addHandler(default_file_handler)
    # 设置全局默认的log级别
    logging.root.setLevel(log_level)

    # 对单独的logger进行配置
    log_cfg: ConfigTree
    for log_cfg in cfg.get_list("log.loggers"):
        logger_name = log_cfg.get_string("name")
        logger_level = log_cfg.get_string("level")
        if force_debug:
            # debug 模式, 强制日志级别为 debug
            logger_level = log_level_name_map["DEBUG"]

        named_logger = logging.getLogger(logger_name)
        set_logger_level(logger = named_logger, logger_level = logger_level)

        log_file_name = log_cfg.get_string("file_name", "")
        if log_file_name != "":
            log_file_backup_count = log_cfg.get_int("backup_count", 3)
            log_file_handler = TimedRotatingFileHandler(filename = log_file_name,
                                                        encoding = 'utf-8',
                                                        when = 'D',
                                                        backupCount = log_file_backup_count,
                                                        atTime = time.fromisoformat("00:00:00"))
            fmt = logging.Formatter(log_fmt)
            log_file_handler.setFormatter(fmt)
            named_logger.addHandler(log_file_handler)


def get_default_logger_level() -> int:
    global log_level_name_map
    cfg = app_config()
    if cfg is not None:
        log_level = cfg.get_string("log.default.level", default = "INFO").upper()
        level = log_level_name_map.get(log_level, logging.INFO)
        return level
    else:
        return logging.INFO


def set_logger_level(logger: logging.Logger, logger_level: str) -> None:
    global log_level_name_map
    levle = log_level_name_map.get(logger_level.upper(), logging.INFO)
    logger.level = levle


def get_logger(name: str) -> logging.Logger:
    return logging.getLogger(name)
