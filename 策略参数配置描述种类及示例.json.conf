{
    "strategyName": "策略名称",
    "strategyId": "a04c65c3-9198-11ed-bae3-00163e163353",
    "version": "v0.0.1",
    "tags": "标签1;标签2;多个标签之间使用分号分隔",
    "parameters": [
        // 文本输入框
        {
            "name": "symbol", // 字段名
            "label": "标的", // 标签名
            "type": "string", // 类型，目前支持 string, number, select, checkbox, time, group, 默认 string；type 为 group 时定义为分组，需要配置 children 字段；
            "default_value": "SHSE.510310", // (通用，可选)默认值，value 如果是队列，则用[ , ]拼接，如 "SHSE.510310,SHSE.510050"
            "description": "描述", // 描述（通用，可选）
            "required": true, // 是否必填（通用，可选）
            "dependent": { // 依赖字段(通用，可选)，[key 字段名]：字段值，作用为满足所需字段的值时，该字段（symbol）才可编辑
                "secType": "STOCK",
                "exchange": "SHSE"
                // ...
            }
        },

        // 数字输入框
        {
            "name": "max_cash",
            "label": "最大资金",
            "type": "number",
            "default_value": 1000000,
            "precision": 2, // （可选）精度小数点后位数，默认 0
            "description": "描述",
            "required": true,
        },

        // 下拉选择框
        {
            "name": "secType",
            "label": "标的类型",
            "type": "select",
            "default_value": "STOCK",
            "description": "描述",
            "required": true,
            "options": [ // 选项列表
                {
                    "label": "股票",
                    "value": "STOCK"
                },
                {
                    "label": "指数",
                    "value": "INDEX"
                }
                // ...
            ]
        },

        // 多选框
        {
            "name": "exchange",
            "label": "交易所",
            "type": "checkbox",
            "default_value": ["SHSE", "SZSE"],
            "options": [
                {
                    "label": "上证",
                    "value": "SHSE"
                },
                {
                    "label": "深证",
                    "value": "SZSE"
                }
                // ...
            ]
        },

        // 时间选择框
        {
            "name": "time",
            "label": "时间",
            "type": "time",
            "default_value": "09:30:00"
        },

        // 日期选择器
        {
            "name": "date",
            "label": "日期",
            "type": "date",
            "default_value": "2023-01-01"
        },

        // 分组
        {
            "name": "groupName", // 分组名
            "label": "分组1",
            "type": "group",
            "children": [ // 子项
                {
                    "name": "symbol",
                    "label": "标的",
                    "type": "string",
                    "default_value": "SHSE.510310"
                },
                // ... 其它
            ]
        },

    ]
}