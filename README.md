# py-strategy-template

能够打包构建成一个独立运行的可执行的python策略程序的工程模板

## 项目文件说明

- 策略基本信息及参数描述配置文件: description.json
- 策略程序启动参数配置文件, 该文件由 **策略管理器** 生成: config.json
- python策略程序文件: quant/strategy.py

## 启动策略程序的规范和约定
- python 策略程序打包构建好之后, 启动的命令行可执行文件名称, 固定为: launcher.exe
- 策略管理器启动策略程序时, 会固定传参数 --config 指定 config.json 文件路径, 例如:
```shell
launcher.exe --config=config.json
```


## 开发环境

### uv 工具介绍
- 官网: https://docs.astral.sh/uv/
- 中文文档: https://hellowac.github.io/uv-zh-cn/

### windows 下安装 uv
```
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### uv 安装 python 3.8
> python 3.8 是最后一个支持 windows 7 的 python 版本
```
uv python install 3.8
```

### 初始化项目的 uv python .ven 环境
在项目目录下执行
```shell
uv sync
```

# 特别注意

## 依赖管理
> 项目的python依赖包, 都需要通过 uv 来添加管理
[参考文档](https://docs.astral.sh/uv/guides/projects/#managing-dependencies)

> 不再依赖的包, 请使用 uv remove 命令删除依赖
 
例如:
```shell
uv add requests
```

# 打包构建

## 策略ID
* 每个策略的 strategyId 都必须是唯一的, 所以每创建一个新的策略, 请修改 description.json 里的 strategyId
* 一旦正式发布策略后, strategyId 就不能再修改了
```shell
# 命令行方式下生成一个uuid
python -c "import uuid; print(uuid.uuid4())"
```

## 设置版本号
* 每次打包前, 请设置 description.json 里的 version 版本号
* 版本号格式: v{主版本号}.{次版本号}.{修订号}

## 打包内容说明
打包程序, 会打包以下的目录和文件
- main.py
- description.json
- resource 目录
- quant 目录

## 打包命令
> __在项目根目录下执行__
```shell
.\package.cmd
```


