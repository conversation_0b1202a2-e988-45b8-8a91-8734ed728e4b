# -*- coding: utf-8 -*-
import os

from pyhocon import ConfigFactory, ConfigTree

cur_dir = os.path.dirname(os.path.realpath(__file__))
defaults_conf_path = os.path.realpath(os.path.join(cur_dir, '../../resource', 'defaults.conf'))
__CONFIG__ = ConfigFactory.parse_file(defaults_conf_path)


def init_config(fpath: str) -> None:
    global __CONFIG__
    if fpath != '':
        custom_config = ConfigFactory.parse_file(filename = fpath)
        __CONFIG__ = ConfigTree.merge_configs(__CONFIG__, custom_config, copy_trees = True)


def app_config() -> ConfigTree:
    global __CONFIG__
    return __CONFIG__
