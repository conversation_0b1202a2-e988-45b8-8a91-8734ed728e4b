include "version.conf"

token = "未设置"

log {
    format = "%(asctime)s [%(name)s] [%(levelname)s] [%(threadName)s] - %(message)s"
    # 全局默认配置, 按天进行日志文件轮换
    default {
        # 日志级别
        level: "INFO"
        # 日志文件名, 为空时, 日志仅仅输出到控制台
        file_name: "logs/strategy.log"
        # 按天保留的
        backup_count: 20
    }

//    # 对指定 logger 的单独配置日志级别
    loggers: [
//        {
//            name: "kts-cli"
//            level: "INFO"
//            file_name: "kts-cli.log"
//            backup_count: 3
//        },
    ]
}