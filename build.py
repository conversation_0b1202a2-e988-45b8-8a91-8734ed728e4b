# -*- coding: utf-8 -*-
import argparse
import datetime
import json
import os
import shutil
import subprocess
import zipfile

import argh

# 获取当前脚本的完整路径
script_path = os.path.abspath(__file__)
# 获取脚本所在目录
script_dir = os.path.dirname(script_path)


def zip_dists():
    """
    压缩dist目录下的文件
    """
    copy_talib_dir()
    copy_resouce_dir()
    # copy_config_file()
    copy_desc_file()
    folder_path = os.path.join(script_dir, 'dist', 'launcher')
    version, strategy_id = get_strategy_info()

    setup_version_conf_file(version)

    zip_path = os.path.join(script_dir, 'dist', f'strategy_{strategy_id}_{version}.zip')
    print(f'开始打包策略,请稍后...')
    zip_folder(folder_path, zip_path)
    print(f'打包完成,请查看: {zip_path}')
    print('请将打包的策略zip文件提交给策略仓库管理员,由其完成后续发布操作')


def copy_resouce_dir():
    src_dir = os.path.join(script_dir, 'resource')
    dst_dir = os.path.join(script_dir, 'dist', 'launcher', '_internal', 'resource')
    shutil.copytree(src_dir, dst_dir, dirs_exist_ok = True)


def copy_talib_dir():
    """
    复制talib目录到dist目录, 修正pyinstaller打包,未能包含完整的talib库的问题
    """
    src_dir = os.path.join(script_dir, '.venv', 'Lib', 'site-packages', 'talib')
    dst_dir = os.path.join(script_dir, 'dist', 'launcher', '_internal', 'talib')
    shutil.copytree(src_dir, dst_dir, dirs_exist_ok = True)
    print('复制talib目录到dist目录, 修正pyinstaller打包,未能包含完整的talib库的问题')

def copy_config_file():
    src_path = os.path.join(script_dir, 'config.json')
    dst_path = os.path.join(script_dir, 'dist', 'launcher', 'config.json')
    shutil.copy(src_path, dst_path)


def copy_desc_file():
    src_path = os.path.join(script_dir, 'description.json')
    dst_path = os.path.join(script_dir, 'dist', 'launcher', 'description.json')
    shutil.copy(src_path, dst_path)


def setup_version_conf_file(version):
    # 获取当前日期和时间
    now = datetime.datetime.now(datetime.timezone(datetime.timedelta(hours = 8)))
    # 格式化日期和时间
    build_at = now.strftime("%Y-%m-%dT%H:%M:%S%z")
    git_hash = try_get_git_hash()
    ver_conf_path = os.path.join(script_dir, 'dist', 'launcher', '_internal', 'resource', 'version.conf')
    with open(ver_conf_path, 'w', encoding = 'utf-8') as f:
        f.writelines([
            f'version="{version}"\n',
            f'git_hash="{git_hash}"\n',
            f'build_at="{build_at}"',
        ])


def try_get_git_hash() -> str:
    try:
        # 执行命令并捕获输出
        result = subprocess.run(
            "git rev-parse HEAD",
            shell = True,
            capture_output = True,
            text = True
        )

        # 检查命令是否成功执行
        if result.returncode == 0:
            # 输出结果
            return result.stdout.strip()
        else:
            print(f"命令执行失败，错误信息：{result.stderr}")
            return '无git版本控制'
    except Exception as e:
        print(f"发生异常：{e}")


def zip_folder(folder_path: str, zip_path: str):
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                zipf.write(file_path, os.path.relpath(file_path, folder_path))


def get_strategy_info() -> (str, str):
    src_path = os.path.join(script_dir, 'description.json')
    with open(src_path, mode = 'r', encoding = 'utf-8') as f:
        json_text = ''.join(f.readlines())
    json_obj = json.loads(json_text)
    return json_obj['version'], json_obj['strategyId']


def clean():
    """
    清理构建目录和发行目录。
    该函数会删除 'build' 和 'dist' 目录及其所有内容。
    """
    # 获取构建目录的路径
    build_dir = os.path.join(script_dir, 'build')
    # 获取发行目录的路径
    dist_dir = os.path.join(script_dir, 'dist')

    # 删除构建目录及其所有内容
    shutil.rmtree(build_dir, ignore_errors = True)
    # 删除发行目录及其所有内容
    shutil.rmtree(dist_dir, ignore_errors = True)


def main():
    parser = argparse.ArgumentParser()
    argh.add_commands(parser, [zip_dists, clean])
    argh.set_default_command(parser, zip_dists)
    argh.dispatch(parser)


if __name__ == "__main__":
    main()
